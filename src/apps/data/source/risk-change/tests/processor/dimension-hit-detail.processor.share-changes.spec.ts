import { Test, TestingModule } from '@nestjs/testing';
import { DimensionHitDetailProcessor } from '../../dimension-hit-detail.processor';
import { BaseHelper } from '../../helper/base.helper';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response';
import { DimensionTypeEnums } from 'libs/enums/diligence/DimensionTypeEnums';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { DimensionDefinitionEntity } from 'libs/entities/DimensionDefinitionEntity';
import { DimensionSourceEnums } from 'libs/enums/diligence/DimensionSourceEnums';
import { <PERSON>Helper } from 'apps/data/helper/person.helper';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { DimensionHitStrategyFieldsEntity } from 'libs/entities/DimensionHitStrategyFieldsEntity';

// 测试辅助函数
const createTestDimensionStrategy = (key: DimensionTypeEnums): DimensionHitStrategyPO => {
  const def = new DimensionDefinitionEntity();
  def.key = key;
  def.name = `测试维度-${key}`;
  def.source = DimensionSourceEnums.RiskChange;
  return new DimensionHitStrategyPO(def);
};

const createTestStrategyField = (fieldKey: DimensionFieldKeyEnums, fieldValue: any): DimensionHitStrategyFieldsEntity => {
  const field = new DimensionHitStrategyFieldsEntity();
  field.dimensionFieldKey = fieldKey;
  field.fieldValue = fieldValue;
  return field;
};

describe('DimensionHitDetailProcessor 股权变更类测试', () => {
  let processor: DimensionHitDetailProcessor;
  let mockRiskChangeHelper: jest.Mocked<RiskChangeHelper>;
  let mockPersonHelper: jest.Mocked<PersonHelper>;
  let mockBaseHelper: jest.Mocked<BaseHelper>;
  let mockSearchEs: jest.Mock;

  beforeEach(async () => {
    // 创建 mock 对象
    mockRiskChangeHelper = {
      hitLayTypesField72: jest.fn(),
      hitIsBPField: jest.fn(),
      category72holderRoleField: jest.fn(),
      category72ShareChangeStatusField: jest.fn(),
      category72isPEVCField: jest.fn(),
      category72periodShareRatioChangeField: jest.fn(),
      hitShareChangeStatusField: jest.fn(),
      hitHolderRoleField: jest.fn(),
      hitDifferenceRatioField: jest.fn(),
      hitAbsRatioField: jest.fn(),
      hitBeforeContentField: jest.fn(),
      hitAfterContentField: jest.fn(),
      hitShareChangeRateField: jest.fn(),
    } as any;

    mockPersonHelper = {
      getEmployeeData: jest.fn(),
    } as any;

    mockBaseHelper = {
      filterLastYearData: jest.fn(),
    } as any;

    mockSearchEs = jest.fn();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionHitDetailProcessor,
        { provide: RiskChangeHelper, useValue: mockRiskChangeHelper },
        { provide: PersonHelper, useValue: mockPersonHelper },
        { provide: BaseHelper, useValue: mockBaseHelper },
      ],
    }).compile();

    processor = module.get<DimensionHitDetailProcessor>(DimensionHitDetailProcessor);
    processor.bindRiskChangeEsSearchFn(mockSearchEs);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('股东股份变更 (category72)', () => {
    it('应该正确处理股东股份变更类型的风险动态', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-shareholder-change',
        Category: RiskChangeCategoryEnum.category72,
        ChangeExtend: JSON.stringify({
          PartInfo: {
            IsBP: '1',
          },
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const layTypesField = createTestStrategyField(DimensionFieldKeyEnums.layTypes, [1]);
      const isBPField = createTestStrategyField(DimensionFieldKeyEnums.isBP, [1]);
      dimension.strategyFields = [layTypesField, isBPField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitLayTypesField72.mockReturnValue(true);
      mockRiskChangeHelper.hitIsBPField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-shareholder-change');
      expect(mockRiskChangeHelper.hitLayTypesField72).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitIsBPField).toHaveBeenCalled();
    });

    it('应该正确处理带有股权角色字段的股东股份变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-shareholder-with-role',
        Category: RiskChangeCategoryEnum.category72,
        ChangeExtend: JSON.stringify({
          PartInfo: {
            IsBP: '1',
          },
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1, 2]);
      dimension.strategyFields = [holderRoleField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.category72holderRoleField.mockResolvedValue({
        hit: true,
        hitKeyNos: ['holder1', 'holder2'],
      });

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-shareholder-with-role');
      expect(mockRiskChangeHelper.category72holderRoleField).toHaveBeenCalledWith(
        holderRoleField,
        expect.objectContaining({
          Id: 'test-shareholder-with-role',
          Category: RiskChangeCategoryEnum.category72,
        }),
        params.keyNo,
      );
    });

    it('应该正确处理周期内持股比例变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-period-share-change',
        Category: RiskChangeCategoryEnum.category72,
        ChangeExtend: JSON.stringify({
          PartInfo: {
            IsBP: '1',
          },
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const periodShareRatioChangeField = createTestStrategyField(DimensionFieldKeyEnums.periodShareRatioChange, [{ timePeriod: 6 }]);
      dimension.strategyFields = [periodShareRatioChangeField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      // Mock ES搜索返回
      mockSearchEs.mockResolvedValue({
        body: {
          hits: {
            total: { value: 5 },
            hits: [
              { _source: { Id: 'period1', Category: RiskChangeCategoryEnum.category72 } },
              { _source: { Id: 'period2', Category: RiskChangeCategoryEnum.category72 } },
            ],
          },
        },
      });

      mockRiskChangeHelper.category72periodShareRatioChangeField.mockResolvedValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-period-share-change');
      expect(mockRiskChangeHelper.category72periodShareRatioChangeField).toHaveBeenCalled();
    });
  });

  describe('股东股份变更 (category44)', () => {
    it('应该正确处理股比下降的股东股份变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-share-decrease',
        Category: RiskChangeCategoryEnum.category44,
        ChangeExtend: JSON.stringify({
          A: 2, // 股比下降count
          D: [
            // 股比下降列表
            { K: 'holder1', B: '30%', C: '20%' },
            { K: 'holder2', B: '25%', C: '15%' },
          ],
          G: 0, // 上升count
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const shareChangeStatusField = createTestStrategyField(DimensionFieldKeyEnums.shareChangeStatus, [0]); // 下降
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1, 2]);
      dimension.strategyFields = [shareChangeStatusField, holderRoleField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitHolderRoleField.mockResolvedValue({
        hit: true,
        hitKeyNos: ['holder1'],
      });

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-share-decrease');
      expect(mockRiskChangeHelper.hitHolderRoleField).toHaveBeenCalledWith(holderRoleField, ['holder1', 'holder2'], params.keyNo);
    });

    it('应该正确处理股比上升的股东股份变更', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-share-increase',
        Category: RiskChangeCategoryEnum.category44,
        ChangeExtend: JSON.stringify({
          A: 0, // 股比下降count
          G: 1, // 上升count
          H: [
            // 上升列表
            { K: 'holder1', B: '20%', C: '30%' },
          ],
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const shareChangeStatusField = createTestStrategyField(DimensionFieldKeyEnums.shareChangeStatus, [1]); // 上升
      const differenceRatioField = createTestStrategyField(DimensionFieldKeyEnums.differenceRatio, [5, 20]);
      dimension.strategyFields = [shareChangeStatusField, differenceRatioField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitDifferenceRatioField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-share-increase');
      expect(mockRiskChangeHelper.hitDifferenceRatioField).toHaveBeenCalled();
    });

    it('应该正确处理变更前后持股比例字段', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-before-after-content',
        Category: RiskChangeCategoryEnum.category44,
        ChangeExtend: JSON.stringify({
          A: 1,
          D: [{ K: 'holder1', B: '30%', C: '20%' }],
          G: 0,
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const shareChangeStatusField = createTestStrategyField(DimensionFieldKeyEnums.shareChangeStatus, [0]);
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1]);
      const beforeContentField = createTestStrategyField(DimensionFieldKeyEnums.beforeContent, [25, 35]);
      const afterContentField = createTestStrategyField(DimensionFieldKeyEnums.afterContent, [15, 25]);
      dimension.strategyFields = [shareChangeStatusField, holderRoleField, beforeContentField, afterContentField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitHolderRoleField.mockResolvedValue({
        hit: true,
        hitKeyNos: ['holder1'],
      });
      mockRiskChangeHelper.hitBeforeContentField.mockReturnValue(true);
      mockRiskChangeHelper.hitAfterContentField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-before-after-content');
      expect(mockRiskChangeHelper.hitBeforeContentField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitAfterContentField).toHaveBeenCalled();
    });
  });

  describe('持股比例变更 (category204/category68)', () => {
    it('应该正确处理持股比例变更 category204', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-holding-ratio-204',
        Category: RiskChangeCategoryEnum.category204,
        ChangeExtend: JSON.stringify({
          K: 'holder1',
          A: '30%',
          B: '25%',
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const shareChangeStatusField = createTestStrategyField(DimensionFieldKeyEnums.shareChangeStatus, [0]); // 下降
      const beforeContentField = createTestStrategyField(DimensionFieldKeyEnums.beforeContent, [25, 35]);
      const afterContentField = createTestStrategyField(DimensionFieldKeyEnums.afterContent, [20, 30]);
      dimension.strategyFields = [shareChangeStatusField, beforeContentField, afterContentField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitShareChangeStatusField.mockReturnValue(true);
      mockRiskChangeHelper.hitBeforeContentField.mockReturnValue(true);
      mockRiskChangeHelper.hitAfterContentField.mockReturnValue(true);

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-holding-ratio-204');
      expect(mockRiskChangeHelper.hitShareChangeStatusField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitBeforeContentField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitAfterContentField).toHaveBeenCalled();
    });

    it('应该正确处理持股比例变更 category68', async () => {
      // Arrange
      const mockItem = {
        Id: 'test-holding-ratio-68',
        Category: RiskChangeCategoryEnum.category68,
        ChangeExtend: JSON.stringify({
          K: 'holder1',
          A: '20%',
          B: '30%',
          IsBP: 1,
        }),
      };

      const detailResp = new HitDetailsBaseResponse();
      detailResp.Result = [mockItem];

      const dimension = createTestDimensionStrategy(DimensionTypeEnums.RiskChange);
      const isBPField = createTestStrategyField(DimensionFieldKeyEnums.isBP, [1]);
      const shareChangeRateField = createTestStrategyField(DimensionFieldKeyEnums.shareChangeRate, [5, 15]);
      const holderRoleField = createTestStrategyField(DimensionFieldKeyEnums.holderRole, [1, 2]);
      dimension.strategyFields = [isBPField, shareChangeRateField, holderRoleField];

      const params = new HitDetailsBaseQueryParams();
      params.keyNo = 'test-company-id';

      mockRiskChangeHelper.hitIsBPField.mockReturnValue(true);
      mockRiskChangeHelper.hitShareChangeRateField.mockReturnValue(true);
      mockRiskChangeHelper.hitHolderRoleField.mockResolvedValue({
        hit: true,
        hitKeyNos: ['holder1'],
      });

      // Act
      const result = await processor.fetchHits(detailResp, dimension, params);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].Id).toBe('test-holding-ratio-68');
      expect(mockRiskChangeHelper.hitIsBPField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitShareChangeRateField).toHaveBeenCalled();
      expect(mockRiskChangeHelper.hitHolderRoleField).toHaveBeenCalledWith(holderRoleField, [params.keyNo], 'holder1');
    });
  });
});
