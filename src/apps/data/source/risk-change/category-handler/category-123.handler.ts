import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { AbstractCategoryHandler } from './abstract-category.handler';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Logger } from 'log4js';

export class Category123Handler extends AbstractCategoryHandler {
  private readonly logger: Logger = QccLogger.getLogger(Category123Handler.name);
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }
  getSupportedCategories(): RiskChangeCategoryEnum[] {
    // 减资公告
    return [RiskChangeCategoryEnum.category123];
  }
  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;
    const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
    if (currencyChangeField && isHit) {
      isHit = this.riskChangeHelper.hitCategory123CurrencyChangeField(currencyChangeField, newItem);
    }
    const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.capitalReductionRate);
    if (regisCapitalChangeRatioField && isHit) {
      isHit = this.riskChangeHelper.capitalReduceSelectCompareResult(regisCapitalChangeRatioField, newItem);
    }
    const changeRangeRegisCapitalCycle = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
    if (changeRangeRegisCapitalCycle && isHit) {
      const valuePeriodBaseLine = changeRangeRegisCapitalCycle.fieldValue[0]?.valuePeriodBaseLine;
      if (valuePeriodBaseLine) {
        const periodRes = await this.riskChangeHelper.commonCivilRiskChange(
          [params.keyNo],
          [RiskChangeCategoryEnum.category37],
          valuePeriodBaseLine,
          'year',
          10000,
        );
        if (periodRes.Paging.TotalRecords > 10000) {
          this.logger.error(`RiskChange category123 Max WindowSize CompanyId ${params.keyNo}`);
        }
        if (periodRes?.Result?.length) {
          isHit = this.riskChangeHelper.hitPeriodRegisCapitalField123(changeRangeRegisCapitalCycle, periodRes.Result, newItem);
        } else {
          isHit = false;
        }
      }
    }
    return isHit;
  }
}
