import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { AbstractCategoryHandler } from './abstract-category.handler';

/**
 * 注册资本变更处理器 (Category 37)
 *
 * 处理注册资本变更的业务逻辑：
 * - 币种变更验证
 * - 注册资本趋势验证
 * - 注册资本变更比例验证
 * - 周期内注册资本变更验证
 */
@Injectable()
export class Category37Handler extends AbstractCategoryHandler {
  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category37];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    // 币种变更验证
    const currencyChangeField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.currencyChange);
    if (currencyChangeField && isHit) {
      isHit = this.riskChangeHelper.hitCurrencyChangeField(currencyChangeField, newItem);
    }

    // 注册资本趋势验证
    const regisCapitalTrendField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalTrend);
    if (regisCapitalTrendField && isHit) {
      isHit = this.riskChangeHelper.hitRegisCapitalTrendField(regisCapitalTrendField, newItem);
    }

    // 注册资本变更比例验证
    const regisCapitalChangeRatioField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.regisCapitalChangeRatio);
    if (regisCapitalChangeRatioField && isHit) {
      isHit = this.riskChangeHelper.hitRegisCapitalChangeRatioField(regisCapitalChangeRatioField, newItem);
    }

    // 周期内注册资本变更验证
    const periodRegisCapitalField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.periodRegisCapital);
    if (periodRegisCapitalField && isHit) {
      const valuePeriodBaseLine = periodRegisCapitalField.fieldValue[0]?.valuePeriodBaseLine;
      if (valuePeriodBaseLine) {
        const periodRes = await this.riskChangeHelper.commonCivilRiskChange([params.keyNo], [RiskChangeCategoryEnum.category37], valuePeriodBaseLine, 'year');
        isHit = this.riskChangeHelper.hitPeriodRegisCapitalField(periodRegisCapitalField, newItem, periodRes.Result);
      }
    }

    return isHit;
  }
}
