# Category Handler 拆分说明

本目录包含了从 `DimensionHitDetailProcessor` 的巨大 switch case 中拆分出来的各个风险变更类别处理器。

## 拆分规则

按照以下规则进行拆分：

1. **复杂业务逻辑**：包含多个字段验证、数据查询、复杂计算的 case，单独创建一个处理器文件
2. **简单处理**：只是简单的方法转发或单一字段验证的 case，放入 `category-simple.handler.ts`

## 文件结构

### 抽象基类

- `abstract-category.handler.ts` - 定义所有处理器的基础结构和通用方法

### 复杂业务逻辑处理器

#### 股权变更相关

- `category-44.handler.ts` - 股东股份变更 (Category 44)

  - 复杂的股份上升/下降逻辑
  - 股东角色验证
  - 差值比例和绝对值比例计算
  - 变更前后持股比例验证

- `category-68-204.handler.ts` - 持股比例变更 (Categories 68, 204)

  - 股份变更状态验证
  - 变更前后持股比例验证
  - 大股东变更状态验证
  - 股份变更率验证

- `category-equity.handler.ts` - 股权相关 (Categories 26, 12, 50)
  - 26: 股权冻结 - 冻结范围、人员、金额验证
  - 12: 股权出质 - 出质比例、状态、数额验证
  - 50: 股权质押 - 质押比例、状态、数量验证

#### 投资变更相关

- `category-17-203.handler.ts` - 对外投资变更 (Categories 17, 203)

  - 变更状态验证
  - 变更前后内容验证
  - 时间周期内投资变更次数验证
  - 变更阈值验证

- `category-37.handler.ts` - 注册资本变更 (Category 37)
  - 币种变更验证
  - 注册资本趋势验证
  - 注册资本变更比例验证
  - 周期内注册资本变更验证

#### 人员变更相关

- `category-46.handler.ts` - 主要人员变更 (Category 46)

  - 人员角色变更验证
  - 基准日期和变更阈值验证
  - 获取员工数据进行比对

- `category-72.handler.ts` - 股东变更 (Category 72)

  - 多种股权变更字段验证
  - PEVC 融资验证
  - 周期内持股比例变更验证

- `category-114.handler.ts` - 受益所有人变更 (Category 114)
  - 股份变更状态验证
  - 股东角色验证
  - 变更前后内容验证

#### 司法案件相关

- `category-judicial.handler.ts` - 司法案件 (Categories 4, 49, 18, 7, 27, 90)
  - 4: 裁判文书, 49: 立案信息, 18: 开庭公告
  - 7: 法院公告, 27: 送达公告, 90: 诉前调解
  - 司法角色、案件类型、金额等多字段验证

#### 处罚相关

- `category-107.handler.ts` - 行政处罚 (Category 107)

  - 处罚单位验证
  - 处罚类型和金额验证
  - 处罚红卡验证
  - 上市状态验证

- `category-123.handler.ts` - 减资公告 (Category 123)
  - 币种变更验证
  - 资本减少比例验证
  - 周期内注册资本验证

### 简单处理器

- `category-simple.handler.ts` - 包含所有简单处理逻辑的 case
  - 新闻相关: Categories 62, 66, 67
  - 基础变更: Categories 39, 139
  - 抵押担保: Categories 15, 30, 53, 101
  - 税务相关: Categories 131, 31, 117
  - 经营状态: Categories 38, 11, 55, 58
  - 司法拍卖: Category 57
  - 处罚相关: Categories 22, 121, 14
  - 知识产权: Category 86
  - 安全相关: Category 79
  - 注销相关: Category 23
  - 融资动态: Category 28
  - 企业公告: Categories 65, 113
  - 评估拍卖: Categories 59, 75
  - 空逻辑: Categories 98, 78, 61, 108, 76

## 使用方式

每个处理器都继承自 `AbstractCategoryHandler`，实现以下方法：

- `getSupportedCategories()` - 返回支持的风险变更类别
- `process()` - 处理具体的业务逻辑
- `preProcess()` - 预处理 JSON 字段（继承自基类）

## 设计原则

1. **单一职责**：每个处理器只处理相关的风险变更类别
2. **逻辑清晰**：复杂逻辑独立成文件，简单逻辑归类到统一文件
3. **易于维护**：通过拆分降低单个文件的复杂度
4. **保持兼容**：不修改原有业务逻辑，只是重新组织代码结构

## 后续使用

在使用这些处理器时，需要：

1. 根据 `Category` 找到对应的处理器
2. 调用处理器的 `process` 方法
3. 获取处理结果

这样的拆分使得代码更容易理解、测试和维护。
