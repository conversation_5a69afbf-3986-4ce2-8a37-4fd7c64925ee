import { QccLogger } from '@kezhaozhao/qcc-logger';
import { Injectable } from '@nestjs/common';
import { RiskChangeHelper } from 'apps/data/helper/risk.change.helper';
import { DimensionFieldKeyEnums } from 'libs/enums/dimension/dimension.filter.params';
import { RiskChangeCategoryEnum } from 'libs/enums/riskchange/RiskChangeCategoryEnum';
import { HitDetailsBaseQueryParams } from 'libs/model/diligence/details/request/HitDetailsBaseQueryParams';
import { RiskChangeItem } from 'libs/model/diligence/details/response/RiskChangeResponse';
import { DimensionHitStrategyPO } from 'libs/model/diligence/dimension/DimensionHitStrategyPO';
import { Logger } from 'log4js';
import { AbstractCategoryHandler } from './abstract-category.handler';
import { HitDetailsBaseResponse } from 'libs/model/diligence/details/response/HitDetailsBaseResponse';

/**
 * 对外投资变更处理器 (Category 17, 203)
 *
 * 处理对外投资变更的复杂业务逻辑：
 * - 变更状态验证
 * - 变更前后内容验证
 * - 大股东变更状态验证
 * - 时间周期内投资变更次数验证
 * - 变更阈值验证
 */
@Injectable()
export class Category17And203Handler extends AbstractCategoryHandler {
  private readonly logger: Logger = QccLogger.getLogger(Category17And203Handler.name);

  constructor(private readonly riskChangeHelper: RiskChangeHelper) {
    super();
  }

  getSupportedCategories(): RiskChangeCategoryEnum[] {
    return [RiskChangeCategoryEnum.category203, RiskChangeCategoryEnum.category17];
  }

  async process(item: RiskChangeItem, dimension: DimensionHitStrategyPO, params: HitDetailsBaseQueryParams): Promise<boolean> {
    const newItem = this.preProcess(item);
    let isHit = true;

    // 变更状态验证
    const changeStatusField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.changeStatus);
    if (changeStatusField && isHit) {
      isHit = this.riskChangeHelper.hitChangeStatusField(changeStatusField, newItem);
    }

    // 变更后内容验证
    const afterContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.afterContent);
    if (afterContentField && isHit) {
      isHit = this.riskChangeHelper.hitAfterContentField(afterContentField, newItem);
    }

    // 大股东变更状态验证
    const isBPField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.isBP);
    if (isBPField && isHit) {
      isHit = this.riskChangeHelper.hitIsBPField(isBPField, newItem);
    }

    // 变更前内容验证
    const beforeContentField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.beforeContent);
    if (beforeContentField && isHit) {
      isHit = this.riskChangeHelper.hitBeforeContentField(beforeContentField, newItem);
    }

    // 时间周期验证
    const timePeriodField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.timePeriod);
    let periodRes: HitDetailsBaseResponse = new HitDetailsBaseResponse();
    if (timePeriodField && isHit) {
      // 获取时间周期内的所有对外投资变更记录
      periodRes = await this.riskChangeHelper.commonCivilRiskChange(
        [params.keyNo],
        [RiskChangeCategoryEnum.category17],
        timePeriodField.fieldValue[0],
        'month',
        10000,
      );
      if (periodRes.Paging.TotalRecords > 10000) {
        // 先记录，超过10000 ，代码需要优化改成游标查询
        this.logger.error(`RiskChange category17 Max WindowSize CompanyId ${params.keyNo}`);
      }
      if (periodRes?.Result?.length) {
        isHit = true;
      } else {
        isHit = false;
      }
    }

    // 变更阈值验证
    const thresholdCountField = dimension.getStrategyFieldByKey(DimensionFieldKeyEnums.thresholdCount);
    if (thresholdCountField && isHit) {
      if (periodRes?.Result?.length) {
        const allPeriodList = periodRes.Result;
        isHit = this.riskChangeHelper.hitTimePeriodThresholdCountField(thresholdCountField, allPeriodList);
      } else {
        isHit = false;
      }
    }

    return isHit;
  }
}
